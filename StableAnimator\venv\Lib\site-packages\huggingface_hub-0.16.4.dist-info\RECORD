../../Scripts/huggingface-cli.exe,sha256=u5EDHULGtkJf_rmjv_iZVu6_63YJMNXEpjc_NIelX54,108431
huggingface_hub-0.16.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
huggingface_hub-0.16.4.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
huggingface_hub-0.16.4.dist-info/METADATA,sha256=Y64NSr287M9QgokPD04nQUJM6yh0Y5dEI8Hct7-KqCM,12020
huggingface_hub-0.16.4.dist-info/RECORD,,
huggingface_hub-0.16.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
huggingface_hub-0.16.4.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
huggingface_hub-0.16.4.dist-info/entry_points.txt,sha256=Y3Z2L02rBG7va_iE6RPXolIgwOdwUFONyRN3kXMxZ0g,131
huggingface_hub-0.16.4.dist-info/top_level.txt,sha256=8KzlQJAY4miUvjAssOAJodqKOw3harNzuiwGQ9qLSSk,16
huggingface_hub/__init__.py,sha256=Vy37hSBL6IN9zAGF4-s02dxKKodb-aFQ8aqHhLT0zsE,17256
huggingface_hub/__pycache__/__init__.cpython-311.pyc,,
huggingface_hub/__pycache__/_commit_api.cpython-311.pyc,,
huggingface_hub/__pycache__/_commit_scheduler.cpython-311.pyc,,
huggingface_hub/__pycache__/_login.cpython-311.pyc,,
huggingface_hub/__pycache__/_multi_commits.cpython-311.pyc,,
huggingface_hub/__pycache__/_snapshot_download.cpython-311.pyc,,
huggingface_hub/__pycache__/_space_api.cpython-311.pyc,,
huggingface_hub/__pycache__/_tensorboard_logger.cpython-311.pyc,,
huggingface_hub/__pycache__/_webhooks_payload.cpython-311.pyc,,
huggingface_hub/__pycache__/_webhooks_server.cpython-311.pyc,,
huggingface_hub/__pycache__/community.cpython-311.pyc,,
huggingface_hub/__pycache__/constants.cpython-311.pyc,,
huggingface_hub/__pycache__/fastai_utils.cpython-311.pyc,,
huggingface_hub/__pycache__/file_download.cpython-311.pyc,,
huggingface_hub/__pycache__/hf_api.cpython-311.pyc,,
huggingface_hub/__pycache__/hf_file_system.cpython-311.pyc,,
huggingface_hub/__pycache__/hub_mixin.cpython-311.pyc,,
huggingface_hub/__pycache__/inference_api.cpython-311.pyc,,
huggingface_hub/__pycache__/keras_mixin.cpython-311.pyc,,
huggingface_hub/__pycache__/lfs.cpython-311.pyc,,
huggingface_hub/__pycache__/repocard.cpython-311.pyc,,
huggingface_hub/__pycache__/repocard_data.cpython-311.pyc,,
huggingface_hub/__pycache__/repository.cpython-311.pyc,,
huggingface_hub/_commit_api.py,sha256=m2b_mUp4AWOxJJTbEq7UhSs5fCp8yVaZyhvZLWKdwMk,25767
huggingface_hub/_commit_scheduler.py,sha256=vitJCSpveiIWCfubFBhE0ryAVgBEyEOsXUQyhx5_Amw,13077
huggingface_hub/_login.py,sha256=EhMnvsJB83vaKkiMrLOwPrFQEqF7c-9iTHXjqditeVA,14261
huggingface_hub/_multi_commits.py,sha256=j1Pvo-c8H5lWIh1lixIWCE4KXTBMK5f2gc7o8U6wSoo,12502
huggingface_hub/_snapshot_download.py,sha256=7hG0nurMyawmIuQhELRh4zd39lKUJ7mKZxq32H1jkPs,11619
huggingface_hub/_space_api.py,sha256=30ZvACXgPjHvni4wKO-3zsLkzj49jD4PTsHDMc4bA00,3597
huggingface_hub/_tensorboard_logger.py,sha256=gp-15kbroQwfXdxdottdLUgh6eQqIUiPwxgAFK5t96w,6632
huggingface_hub/_webhooks_payload.py,sha256=EmPJk4JfrSi-uzliTUtKNY9J0ZbG27rf7yAUt42JgQM,2781
huggingface_hub/_webhooks_server.py,sha256=2wETO28XU2NSLnG-ardYbcLCuVbPL8e_Pl83_4-MngA,14757
huggingface_hub/commands/__init__.py,sha256=AkbM2a-iGh0Vq_xAWhK3mu3uZ44km8-X5uWjKcvcrUQ,928
huggingface_hub/commands/__pycache__/__init__.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/_cli_utils.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/delete_cache.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/env.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/huggingface_cli.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/lfs.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/scan_cache.cpython-311.pyc,,
huggingface_hub/commands/__pycache__/user.cpython-311.pyc,,
huggingface_hub/commands/_cli_utils.py,sha256=VA_3cHzIlsEQmKPnfNTgJNI36UtcrxRmfB44RdbP1LA,1970
huggingface_hub/commands/delete_cache.py,sha256=75qA3TQixIiCULLZumIRj-2rd2JIWeRguWT8jXaemsw,16100
huggingface_hub/commands/env.py,sha256=LJjOxo-m0DrvQdyhWGjnLGtWt91ec63BMI4FQ-5bWXQ,1225
huggingface_hub/commands/huggingface_cli.py,sha256=FaN0T85RYOa5xbU3582wOojA4SWMaRZ4HL41NvI0mX8,1692
huggingface_hub/commands/lfs.py,sha256=rMtm6LdKOGC15aSOXJjOlk3e-bhg7-wlY3yNjEhG8Tc,7332
huggingface_hub/commands/scan_cache.py,sha256=S5Xfnb4ype5aQCojo8vrmEUaS8vX2aozbkD0S1qsJ4M,5152
huggingface_hub/commands/user.py,sha256=aPnSVO4OU86rb8NAL43s-5i2QDBfj0UoeubBJLJ1i8M,6955
huggingface_hub/community.py,sha256=JegzIUDPfqUB4FjZkgDEL5dn8XJIWzJCey-D4pUFNX0,12114
huggingface_hub/constants.py,sha256=fl1twS_ADcLFpH29A-3eMlYeNTYXKpNAxotfYSU_Hv0,4874
huggingface_hub/fastai_utils.py,sha256=5I7zAfgHJU_mZnxnf9wgWTHrCRu_EAV8VTangDVfE_o,16676
huggingface_hub/file_download.py,sha256=4yKdseYb10x_i3EXXqWVYEoTf3PT8tIVBc_pI4Xu0kU,68629
huggingface_hub/hf_api.py,sha256=VWtpYuyfYAqLmRlWFLLVfgG7gqlFlhNMowr4OthaYZ8,211189
huggingface_hub/hf_file_system.py,sha256=a0pHeAcnaN1VP-u7GlgCjOW6oOAahkluE2NztIKuySk,19482
huggingface_hub/hub_mixin.py,sha256=owjmMbrZ_LjbURBrGp99XmHMXiPAj3cDdosG92mkLpI,16130
huggingface_hub/inference/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
huggingface_hub/inference/__pycache__/__init__.cpython-311.pyc,,
huggingface_hub/inference/__pycache__/_client.cpython-311.pyc,,
huggingface_hub/inference/__pycache__/_common.cpython-311.pyc,,
huggingface_hub/inference/__pycache__/_text_generation.cpython-311.pyc,,
huggingface_hub/inference/__pycache__/_types.cpython-311.pyc,,
huggingface_hub/inference/_client.py,sha256=d9OgiSqiSCI8WLBpHB9yzkxMZN290NkVCCz0_OJ9qMs,54278
huggingface_hub/inference/_common.py,sha256=ztVfYEw-EkShFj7YTR7wIxuCIkFpONjli_glLX0ii2U,9312
huggingface_hub/inference/_generated/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
huggingface_hub/inference/_generated/__pycache__/__init__.cpython-311.pyc,,
huggingface_hub/inference/_generated/__pycache__/_async_client.cpython-311.pyc,,
huggingface_hub/inference/_generated/_async_client.py,sha256=24-eazZx-wp49llUR13BddDLWcWS5iQWeYWFZGAGQyM,55630
huggingface_hub/inference/_text_generation.py,sha256=LxEBdkmE_jHByvJW1HMEAQ870MUrp9aoNWTuwiu5iXk,17020
huggingface_hub/inference/_types.py,sha256=iEgkcujrhxIx4P9eFoSXg4c0A_TYXcww59mcmhSJfRw,2678
huggingface_hub/inference_api.py,sha256=R_1pTT1iNNR9w0h_LnpK3zCv609AIBqL9_YNJDHqZBA,8409
huggingface_hub/keras_mixin.py,sha256=BG_Ck_dbaKecW2y2keGXAxVSpPyLACLmWVsd2larcJU,18837
huggingface_hub/lfs.py,sha256=7a5wXF1ZxpknLigpH4WzfArTN8KxDPB_1UXFrSN9ans,17910
huggingface_hub/repocard.py,sha256=8Nl0m0SN6L-Iv3cmWXCo-b-dtHrI0PpppCwxWc-9chc,34273
huggingface_hub/repocard_data.py,sha256=GBcgbCUV4kcwht0VExoUJpIogxZUnGQhR2qQBeD34AE,29580
huggingface_hub/repository.py,sha256=u1-ttpIjHbP9t3Scca62juR3jNs9k_-wQpOZ-59JQH8,53729
huggingface_hub/templates/datasetcard_template.md,sha256=9RRyo88T8dv5oNfjgEGX2NhCR0vW_Y-cGlvfbJ7J5Ro,2721
huggingface_hub/templates/modelcard_template.md,sha256=zgehAu_isrwVL6g1Q3fDIgCHPjjsppO6U9IYlsSaoyI,6766
huggingface_hub/utils/__init__.py,sha256=JyjkiqddwtpZNg2EQt16Vu77Glf5bE7Ca2TOhX7Y1Vw,2985
huggingface_hub/utils/__pycache__/__init__.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_cache_assets.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_cache_manager.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_chunk_utils.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_datetime.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_deprecation.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_errors.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_experimental.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_fixes.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_git_credential.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_headers.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_hf_folder.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_http.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_pagination.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_paths.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_runtime.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_subprocess.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_telemetry.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_typing.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/_validators.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/endpoint_helpers.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/logging.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/sha.cpython-311.pyc,,
huggingface_hub/utils/__pycache__/tqdm.cpython-311.pyc,,
huggingface_hub/utils/_cache_assets.py,sha256=QmI_qlzsOpjK0B6k4GYiLFrKzq3mFLQVoTn2pmWqshE,5755
huggingface_hub/utils/_cache_manager.py,sha256=MvcmmpdxPaYQCMKSLoFPwgPQ0U4cxnnL4DuP440XOhU,29154
huggingface_hub/utils/_chunk_utils.py,sha256=6VRyjiGr2bPupPl1azSUTxKuJ51wdgELipwJ2YRfH5U,2129
huggingface_hub/utils/_datetime.py,sha256=RA92d7k3pV6JKmRXFvsofgp1oHfjMZb5Y-X0vpTIkgQ,2728
huggingface_hub/utils/_deprecation.py,sha256=S3WAbTRVEzmIXxsvZv7egB0QhQDQGxoqDh0i_N3hJ40,8360
huggingface_hub/utils/_errors.py,sha256=ixkt3UIadKuwJs1Ryt0zDwtsEwAC8s6RlzpevWR3HJI,12812
huggingface_hub/utils/_experimental.py,sha256=RvK3nQF7EoPeJf0mbneZ8J6u8AxMdqKyQcpp0Ww-mEE,2442
huggingface_hub/utils/_fixes.py,sha256=wFvfTYj62Il2OwkQB_Qp0xONG6SARQ5oEkT3_FhB4rc,2437
huggingface_hub/utils/_git_credential.py,sha256=8tOMTZBPTm7Z2nTw-6OknP6BW9zglLJK-wwiPI9FxIM,4047
huggingface_hub/utils/_headers.py,sha256=OwP8mzgMwXKLhjwRPwy0Q6-NeQLOgitF2CHuF8o8rzs,9358
huggingface_hub/utils/_hf_folder.py,sha256=DxvQF3gbsXy1NR4sBR5a6IaLxkkTqYyKqrPWvsx1Cqo,3710
huggingface_hub/utils/_http.py,sha256=7-floi1gWz3IgDQ3oizlMSAt3WW5Rm1dfecwnK9wOFA,12003
huggingface_hub/utils/_pagination.py,sha256=VfpmMLyNCRo24fw0o_yWysMK69d9M6sSg2-nWtuypO4,1840
huggingface_hub/utils/_paths.py,sha256=nUaxXN-R2EcWfHE8ivFWfHqEKMIvXEdUeCGDC_QHMqc,4397
huggingface_hub/utils/_runtime.py,sha256=zi4dDu4sGEDhNyVEebmoaZTT-DwZVxqzjC9PXfsxD3k,9092
huggingface_hub/utils/_subprocess.py,sha256=LW9b8TWh9rsm3pW9_5b-mVV_AtYNyLXgC6e09SthkWI,4616
huggingface_hub/utils/_telemetry.py,sha256=jHAdgWNcL9nVvMT3ec3i78O-cwL09GnlifuokzpQjMI,4641
huggingface_hub/utils/_typing.py,sha256=6iafjHSr4mGWyaOOs54AwDPTaAItA_BnN9aGIHVlbYA,1070
huggingface_hub/utils/_validators.py,sha256=3ZmHubjslDRwFYe1oKyaUw6DZrc3DsuV2gABPrx7PTw,9358
huggingface_hub/utils/endpoint_helpers.py,sha256=KCIjPkv2goSpD6b4EI6TIPK28lyQaewV3eUw5yMlrCY,12857
huggingface_hub/utils/logging.py,sha256=-Uov2WGLv16hS-V4Trvs_XO-TvSeZ1xIWZ-cfvDl7ac,4778
huggingface_hub/utils/sha.py,sha256=Wmeh2lwS2H0_CNLqDk_mNyb4jAjfTdlG6FZsuh6LqVg,890
huggingface_hub/utils/tqdm.py,sha256=rgz6ABv578cT9oTu6zkpW1bp21D1E8nH7saUNWcUp5s,6070
